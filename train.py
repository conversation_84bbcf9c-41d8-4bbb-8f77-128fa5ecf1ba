# train.py

import os
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image, ImageOps, ImageFilter

# ----------------------------
# 1. Define character set globally
# ----------------------------
CHARS = "abcdefghijklmnopqrstuvwxyz0123456789"
NUM_CLASSES = len(CHARS)
PAD_IDX = NUM_CLASSES       # extra class for padding
MAX_LEN = 6                 # maximum CAPTCHA length

char_to_idx = {c: i for i, c in enumerate(CHARS)}
idx_to_char = {i: c for i, c in enumerate(CHARS)}

# ----------------------------
# 2. Dataset class
# ----------------------------
class CaptchaDataset(Dataset):
    def __init__(self, root, transform=None, max_len=MAX_LEN):
        self.root = root
        self.transform = transform
        self.max_len = max_len
        # only image files
        self.files = [f for f in os.listdir(root) if f.lower().endswith((".jpg", ".png", ".jpeg"))]

    def __len__(self):
        return len(self.files)

    def __getitem__(self, idx):
        file = self.files[idx]
        path = os.path.join(self.root, file)

        # load image
        image = Image.open(path).convert("RGB")
        # preprocessing
        image = ImageOps.grayscale(image)
        image = ImageOps.autocontrast(image)
        image = image.filter(ImageFilter.MedianFilter(size=3))

        if self.transform:
            image = self.transform(image)

        # extract label
        label_str = os.path.splitext(file)[0]
        label = [char_to_idx[c] for c in label_str]

        # pad to max_len
        orig_len = len(label)
        if orig_len < self.max_len:
            label += [PAD_IDX] * (self.max_len - orig_len)

        return image, torch.tensor(label), orig_len  # return original length too

# ----------------------------
# 3. Data transforms
# ----------------------------
transform = transforms.Compose([
    transforms.Resize((64, 200)),
    transforms.ToTensor()
])

# ----------------------------
# 4. Data loaders
# ----------------------------
train_ds = CaptchaDataset("data/train", transform)
val_ds = CaptchaDataset("data/val", transform)

train_dl = DataLoader(train_ds, batch_size=8, shuffle=True)
val_dl = DataLoader(val_ds, batch_size=8)

# ----------------------------
# 5. Model definition
# ----------------------------
class CaptchaModel(nn.Module):
    def __init__(self, num_classes=NUM_CLASSES+1, max_len=MAX_LEN):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(1, 32, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
            nn.Conv2d(32, 64, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
            nn.Conv2d(64, 128, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
        )
        self.fc = nn.Linear(128 * 8 * 25, max_len * num_classes)
        self.num_classes = num_classes
        self.max_len = max_len

    def forward(self, x):
        x = self.conv(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        x = x.view(-1, self.max_len, self.num_classes)
        return x

# ----------------------------
# 6. Training setup
# ----------------------------
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

model = CaptchaModel().to(device)
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)

# ----------------------------
# 7. Training loop
# ----------------------------
EPOCHS = 20
for epoch in range(EPOCHS):
    model.train()
    total_loss = 0
    for imgs, labels, lengths in train_dl:
        imgs, labels = imgs.to(device), labels.to(device)

        outputs = model(imgs)  # [batch, max_len, num_classes]

        loss = 0
        for i in range(MAX_LEN):
            mask = (labels[:, i] != PAD_IDX)
            if mask.sum() == 0:
                continue
            loss += criterion(outputs[mask, i, :], labels[mask, i])

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        total_loss += loss.item()

    # validation
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for imgs, labels, lengths in val_dl:
            imgs, labels = imgs.to(device), labels.to(device)
            outputs = model(imgs)
            preds = outputs.argmax(dim=2)  # [batch, max_len]

            for i in range(len(labels)):
                orig_len = lengths[i]
                if torch.equal(preds[i, :orig_len], labels[i, :orig_len]):
                    correct += 1
                total += 1

    if total > 0:
        print(f"Epoch {epoch+1}/{EPOCHS} - Loss: {total_loss/len(train_dl):.4f} "
              f"- Val Acc: {correct}/{total} = {100*correct/total:.2f}%")
    else:
        print(f"Epoch {epoch+1}/{EPOCHS} - Loss: {total_loss/len(train_dl):.4f} - No val data")

# ----------------------------
# 8. Save model
# ----------------------------
torch.save(model.state_dict(), "captcha_model.pth")
print("✅ Training complete. Model saved as captcha_model.pth")
